# 心算训练APP (Mental Math Trainer)

一款基于Flutter开发的跨平台心算训练应用，帮助用户通过数学计算练习提升心算能力。

## 功能特色

- 🎯 **多难度训练**：简单、中等、困难三个等级
- ⏱️ **限时挑战**：每题30秒，10题一轮
- 🧮 **全面运算**：加法、减法、乘法、除法
- 📊 **详细统计**：正确率、用时分析
- 📱 **优雅界面**：Material Design + 流畅动画
- 💾 **本地存储**：自动保存训练记录

## 快速开始

### 环境要求

- Flutter SDK 3.32.0+
- Dart SDK 3.8.0+
- Android SDK (API 21+) 或 iOS 12.0+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/mental_math_trainer.git
   cd mental_math_trainer
   ```

2. **安装依赖**
   ```bash
   flutter pub get
   ```

3. **运行应用**
   ```bash
   flutter run
   ```

4. **构建发布版本**
   ```bash
   # Android APK
   flutter build apk --release

   # iOS IPA
   flutter build ios --release
   ```

## 项目结构

```
lib/
├── constants/          # 应用常量配置
├── models/            # 数据模型
├── screens/           # 页面组件
├── services/          # 业务服务
└── main.dart         # 应用入口

docs/                  # 文档目录
├── 初步设计.md        # 设计文档
├── 项目总结.md        # 项目总结
└── 使用说明.md        # 使用指南
```

## 核心功能

### 训练模式
- **简单模式**：个位数运算 (1-9)
- **中等模式**：两位数运算 (10-99)
- **困难模式**：三位数或小数运算

### 统计分析
- 实时正确率计算
- 详细用时统计
- 历史记录追踪
- 成绩趋势分析

### 用户体验
- 直观的界面设计
- 流畅的页面动画
- 即时的答题反馈
- 个性化鼓励系统

## 技术栈

- **前端框架**：Flutter 3.32.0
- **开发语言**：Dart
- **状态管理**：StatefulWidget
- **本地存储**：SharedPreferences
- **UI设计**：Material Design 3
- **动画系统**：Flutter Animation

## 开发指南

### 代码规范
- 遵循Dart官方代码规范
- 使用有意义的变量和函数命名
- 添加必要的注释和文档

### 测试
```bash
# 运行所有测试
flutter test

# 代码分析
flutter analyze
```

### 调试
```bash
# 调试模式运行
flutter run --debug

# 性能分析
flutter run --profile
```

## 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 版本历史

### v1.0.0 (2024-12-21)
- ✨ 初始版本发布
- 🎯 实现三难度训练模式
- 📊 添加详细统计功能
- 💾 支持本地数据存储
- 🎨 优化UI设计和动画

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目地址：[GitHub Repository](https://github.com/your-username/mental_math_trainer)
- 问题反馈：[Issues](https://github.com/your-username/mental_math_trainer/issues)

## 致谢

感谢Flutter团队提供优秀的跨平台开发框架，以及所有为开源社区做出贡献的开发者们。

---

**开始你的心算训练之旅吧！** 🚀
