import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

/// 响应式设计工具类
class ResponsiveUtils {
  /// 获取屏幕类型
  static ScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < AppConstants.breakpointMobile) {
      return ScreenType.mobile;
    } else if (width < AppConstants.breakpointTablet) {
      return ScreenType.tablet;
    } else if (width < AppConstants.breakpointDesktop) {
      return ScreenType.desktop;
    } else {
      return ScreenType.wide;
    }
  }

  /// 判断是否为移动设备
  static bool isMobile(BuildContext context) {
    return getScreenType(context) == ScreenType.mobile;
  }

  /// 判断是否为平板设备
  static bool isTablet(BuildContext context) {
    return getScreenType(context) == ScreenType.tablet;
  }

  /// 判断是否为桌面设备
  static bool isDesktop(BuildContext context) {
    final screenType = getScreenType(context);
    return screenType == ScreenType.desktop || screenType == ScreenType.wide;
  }

  /// 获取响应式值
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
    T? wide,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile;
      case ScreenType.desktop:
        return desktop ?? tablet ?? mobile;
      case ScreenType.wide:
        return wide ?? desktop ?? tablet ?? mobile;
    }
  }

  /// 获取响应式字体大小
  static double getResponsiveFontSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// 获取响应式间距
  static double getResponsiveSpacing(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// 获取响应式列数
  static int getResponsiveColumns(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
      wide: 4,
    );
  }

  /// 获取响应式网格交叉轴数量
  static int getResponsiveCrossAxisCount(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: 2,
      tablet: 3,
      desktop: 4,
      wide: 5,
    );
  }

  /// 获取响应式最大宽度
  static double getResponsiveMaxWidth(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: double.infinity,
      tablet: 600.0,
      desktop: 800.0,
      wide: 1000.0,
    );
  }

  /// 获取响应式内边距
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final spacing = getResponsiveSpacing(
      context,
      mobile: AppConstants.spaceLg,
      tablet: AppConstants.spaceXl,
      desktop: AppConstants.space2Xl,
    );
    
    return EdgeInsets.all(spacing);
  }

  /// 获取响应式水平内边距
  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context) {
    final spacing = getResponsiveSpacing(
      context,
      mobile: AppConstants.spaceLg,
      tablet: AppConstants.spaceXl,
      desktop: AppConstants.space2Xl,
    );
    
    return EdgeInsets.symmetric(horizontal: spacing);
  }

  /// 获取响应式按钮高度
  static double getResponsiveButtonHeight(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: AppConstants.buttonHeightLarge,
      tablet: AppConstants.buttonHeightXLarge,
      desktop: AppConstants.buttonHeightXLarge,
    );
  }

  /// 获取响应式卡片边距
  static EdgeInsets getResponsiveCardMargin(BuildContext context) {
    final spacing = getResponsiveSpacing(
      context,
      mobile: AppConstants.spaceSm,
      tablet: AppConstants.spaceMd,
      desktop: AppConstants.spaceLg,
    );
    
    return EdgeInsets.all(spacing);
  }

  /// 获取响应式卡片内边距
  static EdgeInsets getResponsiveCardPadding(BuildContext context) {
    final spacing = getResponsiveSpacing(
      context,
      mobile: AppConstants.spaceLg,
      tablet: AppConstants.spaceXl,
      desktop: AppConstants.space2Xl,
    );
    
    return EdgeInsets.all(spacing);
  }

  /// 获取安全区域内边距
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: mediaQuery.padding.left,
      right: mediaQuery.padding.right,
    );
  }

  /// 获取键盘高度
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// 判断键盘是否显示
  static bool isKeyboardVisible(BuildContext context) {
    return getKeyboardHeight(context) > 0;
  }

  /// 获取状态栏高度
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取底部安全区域高度
  static double getBottomSafeAreaHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// 获取屏幕方向
  static Orientation getOrientation(BuildContext context) {
    return MediaQuery.of(context).orientation;
  }

  /// 判断是否为横屏
  static bool isLandscape(BuildContext context) {
    return getOrientation(context) == Orientation.landscape;
  }

  /// 判断是否为竖屏
  static bool isPortrait(BuildContext context) {
    return getOrientation(context) == Orientation.portrait;
  }

  /// 获取设备像素比
  static double getDevicePixelRatio(BuildContext context) {
    return MediaQuery.of(context).devicePixelRatio;
  }

  /// 获取文本缩放因子
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaleFactor;
  }

  /// 限制文本缩放因子
  static double getClampedTextScaleFactor(
    BuildContext context, {
    double minScaleFactor = 0.8,
    double maxScaleFactor = 1.3,
  }) {
    final scaleFactor = getTextScaleFactor(context);
    return scaleFactor.clamp(minScaleFactor, maxScaleFactor);
  }
}

/// 屏幕类型枚举
enum ScreenType {
  mobile,
  tablet,
  desktop,
  wide,
}

/// 响应式构建器组件
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, ScreenType screenType) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final screenType = ResponsiveUtils.getScreenType(context);
    return builder(context, screenType);
  }
}

/// 响应式布局组件
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? wide;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.wide,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType) {
        switch (screenType) {
          case ScreenType.mobile:
            return mobile;
          case ScreenType.tablet:
            return tablet ?? mobile;
          case ScreenType.desktop:
            return desktop ?? tablet ?? mobile;
          case ScreenType.wide:
            return wide ?? desktop ?? tablet ?? mobile;
        }
      },
    );
  }
}

/// 响应式容器组件
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? maxWidth;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? ResponsiveUtils.getResponsiveMaxWidth(context),
      ),
      padding: padding ?? ResponsiveUtils.getResponsivePadding(context),
      margin: margin ?? ResponsiveUtils.getResponsiveCardMargin(context),
      child: child,
    );
  }
}
