import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/training_result.dart';

/// 本地存储服务
class StorageService {
  static const String _trainingResultsKey = 'training_results';
  static const int _maxHistoryCount = 5; // 最多保存5条历史记录

  /// 保存训练结果
  static Future<void> saveTrainingResult(TrainingResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 获取现有的训练结果
      List<TrainingResult> results = await getTrainingResults();
      
      // 添加新结果到列表开头
      results.insert(0, result);
      
      // 只保留最近的记录
      if (results.length > _maxHistoryCount) {
        results = results.take(_maxHistoryCount).toList();
      }
      
      // 转换为JSON字符串列表
      List<String> jsonStrings = results
          .map((result) => jsonEncode(result.toJson()))
          .toList();
      
      // 保存到SharedPreferences
      await prefs.setStringList(_trainingResultsKey, jsonStrings);
    } catch (e) {
      debugPrint('保存训练结果失败: $e');
    }
  }

  /// 获取训练结果历史记录
  static Future<List<TrainingResult>> getTrainingResults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 获取JSON字符串列表
      List<String>? jsonStrings = prefs.getStringList(_trainingResultsKey);
      
      if (jsonStrings == null || jsonStrings.isEmpty) {
        return [];
      }
      
      // 转换为TrainingResult对象列表
      List<TrainingResult> results = jsonStrings
          .map((jsonString) {
            try {
              Map<String, dynamic> json = jsonDecode(jsonString);
              return TrainingResult.fromJson(json);
            } catch (e) {
              debugPrint('解析训练结果失败: $e');
              return null;
            }
          })
          .where((result) => result != null)
          .cast<TrainingResult>()
          .toList();
      
      return results;
    } catch (e) {
      debugPrint('获取训练结果失败: $e');
      return [];
    }
  }

  /// 清除所有训练结果
  static Future<void> clearTrainingResults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_trainingResultsKey);
    } catch (e) {
      debugPrint('清除训练结果失败: $e');
    }
  }

  /// 获取最佳成绩统计
  static Future<Map<String, dynamic>> getBestStats() async {
    try {
      List<TrainingResult> results = await getTrainingResults();
      
      if (results.isEmpty) {
        return {
          'bestCorrectRate': 0.0,
          'bestAverageTime': 0.0,
          'totalTrainings': 0,
        };
      }
      
      // 计算最佳正确率
      double bestCorrectRate = results
          .map((result) => result.correctRate)
          .reduce((a, b) => a > b ? a : b);
      
      // 计算最佳平均用时（最短）
      double bestAverageTime = results
          .map((result) => result.averageTimePerQuestion)
          .reduce((a, b) => a < b ? a : b);
      
      return {
        'bestCorrectRate': bestCorrectRate,
        'bestAverageTime': bestAverageTime,
        'totalTrainings': results.length,
      };
    } catch (e) {
      debugPrint('获取最佳成绩失败: $e');
      return {
        'bestCorrectRate': 0.0,
        'bestAverageTime': 0.0,
        'totalTrainings': 0,
      };
    }
  }
}
