import 'dart:math';
import '../models/question.dart';

/// 题目生成服务
class QuestionGenerator {
  static final Random _random = Random();

  /// 生成指定数量和难度的题目列表
  static List<Question> generateQuestions(int count, int difficulty) {
    List<Question> questions = [];
    for (int i = 0; i < count; i++) {
      questions.add(_generateSingleQuestion(difficulty));
    }
    return questions;
  }

  /// 生成单道题目
  static Question _generateSingleQuestion(int difficulty) {
    // 随机选择运算类型
    List<String> operations = ['+', '-', '*', '/'];
    String operation = operations[_random.nextInt(operations.length)];

    switch (difficulty) {
      case 1: // 简单：个位数运算
        return _generateEasyQuestion(operation);
      case 2: // 中等：两位数运算
        return _generateMediumQuestion(operation);
      case 3: // 困难：三位数或小数运算
        return _generateHardQuestion(operation);
      default:
        return _generateEasyQuestion(operation);
    }
  }

  /// 生成简单题目（个位数）
  static Question _generateEasyQuestion(String operation) {
    int num1, num2;
    double answer;
    String expression;

    switch (operation) {
      case '+':
        num1 = _random.nextInt(9) + 1; // 1-9
        num2 = _random.nextInt(9) + 1; // 1-9
        answer = (num1 + num2).toDouble();
        expression = '$num1 + $num2';
        break;
      case '-':
        num1 = _random.nextInt(9) + 1; // 1-9
        num2 = _random.nextInt(num1) + 1; // 1-num1，确保结果为正
        answer = (num1 - num2).toDouble();
        expression = '$num1 - $num2';
        break;
      case '*':
        num1 = _random.nextInt(9) + 1; // 1-9
        num2 = _random.nextInt(9) + 1; // 1-9
        answer = (num1 * num2).toDouble();
        expression = '$num1 × $num2';
        break;
      case '/':
        // 确保整除
        num2 = _random.nextInt(9) + 1; // 1-9
        answer = (_random.nextInt(9) + 1).toDouble(); // 1-9
        num1 = (answer * num2).toInt();
        expression = '$num1 ÷ $num2';
        break;
      default:
        num1 = _random.nextInt(9) + 1;
        num2 = _random.nextInt(9) + 1;
        answer = (num1 + num2).toDouble();
        expression = '$num1 + $num2';
    }

    return Question(
      expression: expression,
      answer: answer,
      operation: operation,
      difficulty: 1,
    );
  }

  /// 生成中等题目（两位数）
  static Question _generateMediumQuestion(String operation) {
    int num1, num2;
    double answer;
    String expression;

    switch (operation) {
      case '+':
        num1 = _random.nextInt(90) + 10; // 10-99
        num2 = _random.nextInt(90) + 10; // 10-99
        answer = (num1 + num2).toDouble();
        expression = '$num1 + $num2';
        break;
      case '-':
        num1 = _random.nextInt(90) + 10; // 10-99
        num2 = _random.nextInt(num1 - 10) + 10; // 10-(num1-1)，确保结果为正
        answer = (num1 - num2).toDouble();
        expression = '$num1 - $num2';
        break;
      case '*':
        num1 = _random.nextInt(90) + 10; // 10-99
        num2 = _random.nextInt(9) + 1; // 1-9，避免结果过大
        answer = (num1 * num2).toDouble();
        expression = '$num1 × $num2';
        break;
      case '/':
        // 确保整除
        num2 = _random.nextInt(9) + 1; // 1-9
        answer = (_random.nextInt(90) + 10).toDouble(); // 10-99
        num1 = (answer * num2).toInt();
        expression = '$num1 ÷ $num2';
        break;
      default:
        num1 = _random.nextInt(90) + 10;
        num2 = _random.nextInt(90) + 10;
        answer = (num1 + num2).toDouble();
        expression = '$num1 + $num2';
    }

    return Question(
      expression: expression,
      answer: answer,
      operation: operation,
      difficulty: 2,
    );
  }

  /// 生成困难题目（三位数或小数）
  static Question _generateHardQuestion(String operation) {
    double answer;
    String expression;

    switch (operation) {
      case '+':
        if (_random.nextBool()) {
          // 三位数加法
          int num1 = _random.nextInt(900) + 100; // 100-999
          int num2 = _random.nextInt(900) + 100; // 100-999
          answer = (num1 + num2).toDouble();
          expression = '$num1 + $num2';
        } else {
          // 小数加法
          double num1 = (_random.nextInt(900) + 100) / 10.0; // 10.0-99.9
          double num2 = (_random.nextInt(900) + 100) / 10.0; // 10.0-99.9
          answer = num1 + num2;
          expression = '${num1.toStringAsFixed(1)} + ${num2.toStringAsFixed(1)}';
        }
        break;
      case '-':
        if (_random.nextBool()) {
          // 三位数减法
          int num1 = _random.nextInt(900) + 100; // 100-999
          int num2 = _random.nextInt(num1 - 100) + 100; // 100-(num1-1)
          answer = (num1 - num2).toDouble();
          expression = '$num1 - $num2';
        } else {
          // 小数减法
          double num1 = (_random.nextInt(900) + 100) / 10.0; // 10.0-99.9
          double num2 = (_random.nextInt((num1 * 10).toInt() - 100) + 100) / 10.0;
          answer = num1 - num2;
          expression = '${num1.toStringAsFixed(1)} - ${num2.toStringAsFixed(1)}';
        }
        break;
      case '*':
        // 三位数乘以个位数
        int num1 = _random.nextInt(900) + 100; // 100-999
        int num2 = _random.nextInt(9) + 1; // 1-9
        answer = (num1 * num2).toDouble();
        expression = '$num1 × $num2';
        break;
      case '/':
        // 确保整除
        int num2 = _random.nextInt(9) + 1; // 1-9
        answer = (_random.nextInt(900) + 100).toDouble(); // 100-999
        int num1 = (answer * num2).toInt();
        expression = '$num1 ÷ $num2';
        break;
      default:
        int num1 = _random.nextInt(900) + 100;
        int num2 = _random.nextInt(900) + 100;
        answer = (num1 + num2).toDouble();
        expression = '$num1 + $num2';
    }

    return Question(
      expression: expression,
      answer: answer,
      operation: operation,
      difficulty: 3,
    );
  }
}
