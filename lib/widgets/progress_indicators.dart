import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../constants/app_constants.dart';

/// 自定义进度指示器
class CustomProgressIndicator extends StatelessWidget {
  final double value;
  final Color? backgroundColor;
  final Color? valueColor;
  final double height;
  final BorderRadius? borderRadius;
  final String? label;
  final bool showPercentage;

  const CustomProgressIndicator({
    super.key,
    required this.value,
    this.backgroundColor,
    this.valueColor,
    this.height = 8.0,
    this.borderRadius,
    this.label,
    this.showPercentage = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null || showPercentage) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (label != null)
                Text(label!, style: theme.textTheme.bodyMedium),
              if (showPercentage)
                Text(
                  '${(value * 100).toInt()}%',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: valueColor ?? theme.colorScheme.primary,
                  ),
                ),
            ],
          ),
          const SizedBox(height: AppConstants.spaceSm),
        ],
        Container(
          height: height,
          decoration: BoxDecoration(
            color: backgroundColor ?? theme.colorScheme.surfaceVariant,
            borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
          ),
          child: ClipRRect(
            borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
            child: LinearProgressIndicator(
              value: value,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                valueColor ?? theme.colorScheme.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 自定义圆形进度指示器
class CustomCircularProgressIndicator extends StatelessWidget {
  final double value;
  final double size;
  final double strokeWidth;
  final Color? backgroundColor;
  final Color? valueColor;
  final Widget? child;
  final String? label;

  const CustomCircularProgressIndicator({
    super.key,
    required this.value,
    this.size = 80.0,
    this.strokeWidth = 8.0,
    this.backgroundColor,
    this.valueColor,
    this.child,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              // 背景圆环
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: strokeWidth,
                  backgroundColor:
                      backgroundColor ?? theme.colorScheme.surfaceVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    backgroundColor ?? theme.colorScheme.surfaceVariant,
                  ),
                ),
              ),
              // 进度圆环
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: value,
                  strokeWidth: strokeWidth,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    valueColor ?? theme.colorScheme.primary,
                  ),
                ),
              ),
              // 中心内容
              if (child != null) Center(child: child!),
            ],
          ),
        ),
        if (label != null) ...[
          const SizedBox(height: AppConstants.spaceSm),
          Text(
            label!,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// 步骤进度指示器
class StepProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final Color? activeColor;
  final Color? inactiveColor;
  final double size;
  final List<String>? labels;

  const StepProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.activeColor,
    this.inactiveColor,
    this.size = 32.0,
    this.labels,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final active = activeColor ?? theme.colorScheme.primary;
    final inactive = inactiveColor ?? theme.colorScheme.surfaceVariant;

    return Column(
      children: [
        Row(
          children: List.generate(totalSteps * 2 - 1, (index) {
            if (index.isEven) {
              // 步骤圆圈
              final stepIndex = index ~/ 2;
              final isActive = stepIndex < currentStep;
              final isCurrent = stepIndex == currentStep;

              return Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: isActive || isCurrent ? active : inactive,
                  shape: BoxShape.circle,
                  border: isCurrent
                      ? Border.all(color: active, width: 2)
                      : null,
                ),
                child: Center(
                  child: isActive
                      ? Icon(Icons.check, color: Colors.white, size: size * 0.6)
                      : Text(
                          '${stepIndex + 1}',
                          style: TextStyle(
                            color: isActive || isCurrent
                                ? Colors.white
                                : theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.bold,
                            fontSize: size * 0.4,
                          ),
                        ),
                ),
              );
            } else {
              // 连接线
              final stepIndex = index ~/ 2;
              final isActive = stepIndex < currentStep;

              return Expanded(
                child: Container(
                  height: 2,
                  color: isActive ? active : inactive,
                ),
              );
            }
          }),
        ),
        if (labels != null) ...[
          const SizedBox(height: AppConstants.spaceSm),
          Row(
            children: List.generate(totalSteps, (index) {
              return Expanded(
                child: Text(
                  labels![index],
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: index <= currentStep
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }),
          ),
        ],
      ],
    );
  }
}

/// 动画进度指示器
class AnimatedProgressIndicator extends StatefulWidget {
  final double value;
  final Duration duration;
  final Color? backgroundColor;
  final Color? valueColor;
  final double height;
  final String? label;
  final bool showPercentage;

  const AnimatedProgressIndicator({
    super.key,
    required this.value,
    this.duration = const Duration(milliseconds: 1000),
    this.backgroundColor,
    this.valueColor,
    this.height = 8.0,
    this.label,
    this.showPercentage = false,
  });

  @override
  State<AnimatedProgressIndicator> createState() =>
      _AnimatedProgressIndicatorState();
}

class _AnimatedProgressIndicatorState extends State<AnimatedProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.value,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.value,
      ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomProgressIndicator(
          value: _animation.value,
          backgroundColor: widget.backgroundColor,
          valueColor: widget.valueColor,
          height: widget.height,
          label: widget.label,
          showPercentage: widget.showPercentage,
        );
      },
    );
  }
}

/// 波浪进度指示器
class WaveProgressIndicator extends StatefulWidget {
  final double value;
  final double size;
  final Color? backgroundColor;
  final Color? waveColor;
  final String? label;

  const WaveProgressIndicator({
    super.key,
    required this.value,
    this.size = 100.0,
    this.backgroundColor,
    this.waveColor,
    this.label,
  });

  @override
  State<WaveProgressIndicator> createState() => _WaveProgressIndicatorState();
}

class _WaveProgressIndicatorState extends State<WaveProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: ClipOval(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return CustomPaint(
                  painter: WavePainter(
                    value: widget.value,
                    animationValue: _controller.value,
                    backgroundColor:
                        widget.backgroundColor ??
                        theme.colorScheme.surfaceVariant,
                    waveColor: widget.waveColor ?? theme.colorScheme.primary,
                  ),
                  size: Size(widget.size, widget.size),
                );
              },
            ),
          ),
        ),
        if (widget.label != null) ...[
          const SizedBox(height: AppConstants.spaceSm),
          Text(
            widget.label!,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// 波浪绘制器
class WavePainter extends CustomPainter {
  final double value;
  final double animationValue;
  final Color backgroundColor;
  final Color waveColor;

  WavePainter({
    required this.value,
    required this.animationValue,
    required this.backgroundColor,
    required this.waveColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // 绘制背景
    paint.color = backgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

    // 绘制波浪
    paint.color = waveColor;
    final path = Path();
    final waveHeight = size.height * (1 - value);

    path.moveTo(0, waveHeight);

    for (double x = 0; x <= size.width; x++) {
      final y =
          waveHeight +
          math.sin(
                (x / size.width * 2 * math.pi) + (animationValue * 2 * math.pi),
              ) *
              10;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
