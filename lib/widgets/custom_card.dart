import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

/// 自定义卡片组件
class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;
  final Border? border;
  final Gradient? gradient;
  final VoidCallback? onTap;
  final bool isClickable;

  const CustomCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.boxShadow,
    this.border,
    this.gradient,
    this.onTap,
    this.isClickable = false,
  });

  /// 基础卡片构造函数
  const CustomCard.basic({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
  }) : backgroundColor = null,
       elevation = null,
       borderRadius = null,
       boxShadow = null,
       border = null,
       gradient = null,
       isClickable = false;

  /// 阴影卡片构造函数
  const CustomCard.elevated({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.onTap,
  }) : elevation = 4,
       borderRadius = null,
       boxShadow = null,
       border = null,
       gradient = null,
       isClickable = false;

  /// 渐变卡片构造函数
  const CustomCard.gradient({
    super.key,
    required this.child,
    required this.gradient,
    this.padding,
    this.margin,
    this.onTap,
  }) : backgroundColor = null,
       elevation = null,
       borderRadius = null,
       boxShadow = null,
       border = null,
       isClickable = false;

  /// 可点击卡片构造函数
  const CustomCard.clickable({
    super.key,
    required this.child,
    required this.onTap,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.boxShadow,
    this.border,
    this.gradient,
  }) : isClickable = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget cardContent = Container(
      padding: padding ?? const EdgeInsets.all(AppConstants.spaceLg),
      decoration: BoxDecoration(
        color: gradient == null ? (backgroundColor ?? theme.cardColor) : null,
        gradient: gradient,
        borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.radiusXl),
        boxShadow: boxShadow ?? _getDefaultBoxShadow(),
        border: border,
      ),
      child: child,
    );

    if (isClickable || onTap != null) {
      cardContent = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.radiusXl),
          child: cardContent,
        ),
      );
    }

    if (margin != null) {
      return Container(
        margin: margin,
        child: cardContent,
      );
    }

    return cardContent;
  }

  /// 获取默认阴影
  List<BoxShadow> _getDefaultBoxShadow() {
    if (elevation != null && elevation! > 0) {
      return [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          offset: Offset(0, elevation! / 2),
          blurRadius: elevation! * 2,
          spreadRadius: 0,
        ),
      ];
    }
    return AppConstants.cardShadow;
  }
}

/// 统计卡片组件
class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final Widget? icon;
  final Color? valueColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    this.icon,
    this.valueColor,
    this.backgroundColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CustomCard.clickable(
      onTap: onTap,
      backgroundColor: backgroundColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            icon!,
            const SizedBox(height: AppConstants.spaceMd),
          ],
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: AppConstants.spaceSm),
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor ?? theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }
}

/// 成绩卡片组件
class ScoreCard extends StatelessWidget {
  final double score;
  final String label;
  final Color? scoreColor;
  final Widget? icon;
  final String? subtitle;

  const ScoreCard({
    super.key,
    required this.score,
    required this.label,
    this.scoreColor,
    this.icon,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CustomCard.elevated(
      child: Column(
        children: [
          if (icon != null) ...[
            icon!,
            const SizedBox(height: AppConstants.spaceMd),
          ],
          Text(
            '${score.toStringAsFixed(score == score.toInt() ? 0 : 1)}${label.contains('%') ? '' : label.contains('秒') ? '' : ''}',
            style: theme.textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: scoreColor ?? _getScoreColor(score),
            ),
          ),
          if (label.contains('%')) 
            Text(
              '%',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: scoreColor ?? _getScoreColor(score),
              ),
            ),
          const SizedBox(height: AppConstants.spaceSm),
          Text(
            label,
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: AppConstants.spaceXs),
            Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// 根据分数获取颜色
  Color _getScoreColor(double score) {
    if (score >= 90) {
      return const Color(AppConstants.successColorValue);
    } else if (score >= 70) {
      return const Color(AppConstants.warningColorValue);
    } else {
      return const Color(AppConstants.errorColorValue);
    }
  }
}

/// 信息卡片组件
class InfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final Color? backgroundColor;

  const InfoCard({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CustomCard.clickable(
      onTap: onTap,
      backgroundColor: backgroundColor,
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: AppConstants.spaceMd),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: AppConstants.spaceXs),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: AppConstants.spaceMd),
            trailing!,
          ],
        ],
      ),
    );
  }
}
