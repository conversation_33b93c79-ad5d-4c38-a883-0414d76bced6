import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

/// 自定义按钮组件
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final Widget? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  });

  /// 主要按钮构造函数
  const CustomButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  }) : type = ButtonType.primary;

  /// 次要按钮构造函数
  const CustomButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  }) : type = ButtonType.secondary;

  /// 轮廓按钮构造函数
  const CustomButton.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  }) : type = ButtonType.outlined;

  /// 文本按钮构造函数
  const CustomButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  }) : type = ButtonType.text;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonStyle = _getButtonStyle(theme);
    final child = _buildButtonChild();

    Widget button;
    switch (type) {
      case ButtonType.primary:
      case ButtonType.secondary:
        button = ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
        break;
      case ButtonType.outlined:
        button = OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
        break;
      case ButtonType.text:
        button = TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
        break;
    }

    if (isFullWidth) {
      return SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return button;
  }

  /// 构建按钮内容
  Widget _buildButtonChild() {
    if (isLoading) {
      return SizedBox(
        height: _getIconSize(),
        width: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            _getForegroundColor(),
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: AppConstants.spaceSm),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  /// 获取按钮样式
  ButtonStyle _getButtonStyle(ThemeData theme) {
    return ButtonStyle(
      backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.disabled)) {
          return theme.disabledColor;
        }
        return backgroundColor ?? _getBackgroundColor();
      }),
      foregroundColor: WidgetStateProperty.all(_getForegroundColor()),
      minimumSize: WidgetStateProperty.all(_getMinimumSize()),
      padding: WidgetStateProperty.all(_getPadding()),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: borderRadius ?? _getBorderRadius(),
        ),
      ),
      side: type == ButtonType.outlined
          ? WidgetStateProperty.all(
              BorderSide(
                color: foregroundColor ?? const Color(AppConstants.primaryColorValue),
                width: 1.5,
              ),
            )
          : null,
      elevation: type == ButtonType.text || type == ButtonType.outlined
          ? WidgetStateProperty.all(0)
          : WidgetStateProperty.all(2),
      textStyle: WidgetStateProperty.all(_getTextStyle()),
    );
  }

  /// 获取背景颜色
  Color _getBackgroundColor() {
    switch (type) {
      case ButtonType.primary:
        return const Color(AppConstants.primaryColorValue);
      case ButtonType.secondary:
        return const Color(AppConstants.secondaryColorValue);
      case ButtonType.outlined:
      case ButtonType.text:
        return Colors.transparent;
    }
  }

  /// 获取前景颜色
  Color _getForegroundColor() {
    switch (type) {
      case ButtonType.primary:
      case ButtonType.secondary:
        return foregroundColor ?? Colors.white;
      case ButtonType.outlined:
      case ButtonType.text:
        return foregroundColor ?? const Color(AppConstants.primaryColorValue);
    }
  }

  /// 获取最小尺寸
  Size _getMinimumSize() {
    switch (size) {
      case ButtonSize.small:
        return const Size(AppConstants.buttonMinWidth, AppConstants.buttonHeightSmall);
      case ButtonSize.medium:
        return const Size(AppConstants.buttonMinWidth, AppConstants.buttonHeightMedium);
      case ButtonSize.large:
        return const Size(AppConstants.buttonMinWidth, AppConstants.buttonHeightLarge);
      case ButtonSize.extraLarge:
        return const Size(AppConstants.buttonMinWidth, AppConstants.buttonHeightXLarge);
    }
  }

  /// 获取内边距
  EdgeInsetsGeometry _getPadding() {
    if (padding != null) return padding!;
    
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.spaceMd,
          vertical: AppConstants.spaceSm,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.spaceLg,
          vertical: AppConstants.spaceMd,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.spaceXl,
          vertical: AppConstants.spaceLg,
        );
      case ButtonSize.extraLarge:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.space2Xl,
          vertical: AppConstants.spaceXl,
        );
    }
  }

  /// 获取圆角
  BorderRadius _getBorderRadius() {
    switch (size) {
      case ButtonSize.small:
        return BorderRadius.circular(AppConstants.radiusLg);
      case ButtonSize.medium:
        return BorderRadius.circular(AppConstants.radiusXl);
      case ButtonSize.large:
      case ButtonSize.extraLarge:
        return BorderRadius.circular(AppConstants.radius2Xl);
    }
  }

  /// 获取文本样式
  TextStyle _getTextStyle() {
    switch (size) {
      case ButtonSize.small:
        return const TextStyle(
          fontSize: AppConstants.bodySmallFontSize,
          fontWeight: FontWeight.w600,
        );
      case ButtonSize.medium:
        return const TextStyle(
          fontSize: AppConstants.bodyMediumFontSize,
          fontWeight: FontWeight.w600,
        );
      case ButtonSize.large:
        return const TextStyle(
          fontSize: AppConstants.bodyLargeFontSize,
          fontWeight: FontWeight.w600,
        );
      case ButtonSize.extraLarge:
        return const TextStyle(
          fontSize: AppConstants.titleMediumFontSize,
          fontWeight: FontWeight.w600,
        );
    }
  }

  /// 获取图标尺寸
  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return AppConstants.iconSizeSmall;
      case ButtonSize.medium:
        return AppConstants.iconSizeMedium;
      case ButtonSize.large:
      case ButtonSize.extraLarge:
        return AppConstants.iconSizeLarge;
    }
  }
}

/// 按钮类型枚举
enum ButtonType {
  primary,
  secondary,
  outlined,
  text,
}

/// 按钮尺寸枚举
enum ButtonSize {
  small,
  medium,
  large,
  extraLarge,
}
