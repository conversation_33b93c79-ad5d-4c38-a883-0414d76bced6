/// 训练结果模型
class TrainingResult {
  final DateTime date; // 训练日期
  final int correctCount; // 正确题数
  final int totalCount; // 总题数
  final int totalTimeSeconds; // 总用时（秒）
  final int difficulty; // 难度等级
  final List<QuestionResult> questionResults; // 每道题的结果

  TrainingResult({
    required this.date,
    required this.correctCount,
    required this.totalCount,
    required this.totalTimeSeconds,
    required this.difficulty,
    required this.questionResults,
  });

  /// 计算正确率（百分比）
  double get correctRate => (correctCount / totalCount) * 100;

  /// 计算平均每题用时（秒）
  double get averageTimePerQuestion => totalTimeSeconds / totalCount;

  /// 从JSON创建TrainingResult对象
  factory TrainingResult.fromJson(Map<String, dynamic> json) {
    return TrainingResult(
      date: DateTime.parse(json['date']),
      correctCount: json['correctCount'],
      totalCount: json['totalCount'],
      totalTimeSeconds: json['totalTimeSeconds'],
      difficulty: json['difficulty'],
      questionResults: (json['questionResults'] as List)
          .map((item) => QuestionResult.fromJson(item))
          .toList(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'correctCount': correctCount,
      'totalCount': totalCount,
      'totalTimeSeconds': totalTimeSeconds,
      'difficulty': difficulty,
      'questionResults': questionResults.map((item) => item.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'TrainingResult{date: $date, correctRate: ${correctRate.toStringAsFixed(1)}%, totalTime: ${totalTimeSeconds}s}';
  }
}

/// 单道题目的结果
class QuestionResult {
  final String expression; // 题目表达式
  final double correctAnswer; // 正确答案
  final double? userAnswer; // 用户答案（可能为空，表示超时未答）
  final bool isCorrect; // 是否正确
  final int timeSeconds; // 用时（秒）

  QuestionResult({
    required this.expression,
    required this.correctAnswer,
    this.userAnswer,
    required this.isCorrect,
    required this.timeSeconds,
  });

  /// 从JSON创建QuestionResult对象
  factory QuestionResult.fromJson(Map<String, dynamic> json) {
    return QuestionResult(
      expression: json['expression'],
      correctAnswer: json['correctAnswer'].toDouble(),
      userAnswer: json['userAnswer']?.toDouble(),
      isCorrect: json['isCorrect'],
      timeSeconds: json['timeSeconds'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'expression': expression,
      'correctAnswer': correctAnswer,
      'userAnswer': userAnswer,
      'isCorrect': isCorrect,
      'timeSeconds': timeSeconds,
    };
  }
}
