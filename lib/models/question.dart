/// 题目模型
class Question {
  final String expression; // 题目表达式，如 "23 + 47"
  final double answer; // 正确答案
  final String operation; // 运算类型：+, -, *, /
  final int difficulty; // 难度等级：1-简单，2-中等，3-困难

  Question({
    required this.expression,
    required this.answer,
    required this.operation,
    required this.difficulty,
  });

  /// 从JSON创建Question对象
  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      expression: json['expression'],
      answer: json['answer'].toDouble(),
      operation: json['operation'],
      difficulty: json['difficulty'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'expression': expression,
      'answer': answer,
      'operation': operation,
      'difficulty': difficulty,
    };
  }

  @override
  String toString() {
    return 'Question{expression: $expression, answer: $answer, operation: $operation, difficulty: $difficulty}';
  }
}
