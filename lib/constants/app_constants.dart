import 'package:flutter/material.dart';

/// 应用常量 - 现代化设计系统
class AppConstants {
  // 应用信息
  static const String appName = '心算训练';
  static const String appVersion = '1.0.0';

  // 训练设置
  static const int questionsPerTraining = 10; // 每次训练的题目数量
  static const int timePerQuestionSeconds = 30; // 每道题的时间限制（秒）

  // 难度等级
  static const int difficultyEasy = 1;
  static const int difficultyMedium = 2;
  static const int difficultyHard = 3;

  // 难度名称映射
  static const Map<int, String> difficultyNames = {
    difficultyEasy: '简单',
    difficultyMedium: '中等',
    difficultyHard: '困难',
  };

  // 难度描述
  static const Map<int, String> difficultyDescriptions = {
    difficultyEasy: '个位数运算',
    difficultyMedium: '两位数运算',
    difficultyHard: '三位数或小数运算',
  };

  // === 现代化颜色系统 ===

  // 主色调 - 深蓝紫色系
  static const int primaryColorValue = 0xFF6366F1; // Indigo-500
  static const int primaryLightColorValue = 0xFF818CF8; // Indigo-400
  static const int primaryDarkColorValue = 0xFF4F46E5; // Indigo-600

  // 次要色调 - 青色系
  static const int secondaryColorValue = 0xFF06B6D4; // Cyan-500
  static const int secondaryLightColorValue = 0xFF22D3EE; // Cyan-400
  static const int secondaryDarkColorValue = 0xFF0891B2; // Cyan-600

  // 强调色 - 绿色系
  static const int accentColorValue = 0xFF10B981; // Emerald-500
  static const int accentLightColorValue = 0xFF34D399; // Emerald-400
  static const int accentDarkColorValue = 0xFF059669; // Emerald-600

  // 状态颜色
  static const int successColorValue = 0xFF10B981; // Emerald-500
  static const int warningColorValue = 0xFFF59E0B; // Amber-500
  static const int errorColorValue = 0xFFEF4444; // Red-500
  static const int infoColorValue = 0xFF3B82F6; // Blue-500

  // 中性色系
  static const int surfaceColorValue = 0xFFFAFAFA; // Gray-50
  static const int backgroundColorValue = 0xFFFFFFFF; // White
  static const int cardColorValue = 0xFFFFFFFF; // White
  static const int dividerColorValue = 0xFFE5E7EB; // Gray-200

  // 文本颜色
  static const int textPrimaryColorValue = 0xFF111827; // Gray-900
  static const int textSecondaryColorValue = 0xFF6B7280; // Gray-500
  static const int textTertiaryColorValue = 0xFF9CA3AF; // Gray-400
  static const int textOnPrimaryColorValue = 0xFFFFFFFF; // White

  // 渐变色定义
  static const List<Color> primaryGradient = [
    Color(0xFF6366F1), // Indigo-500
    Color(0xFF8B5CF6), // Violet-500
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF06B6D4), // Cyan-500
    Color(0xFF3B82F6), // Blue-500
  ];

  static const List<Color> accentGradient = [
    Color(0xFF10B981), // Emerald-500
    Color(0xFF059669), // Emerald-600
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFFF8FAFC), // Slate-50
    Color(0xFFFFFFFF), // White
  ];

  // === 阴影系统 ===

  // 卡片阴影
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: const Color(0xFF000000).withOpacity(0.04),
      offset: const Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: const Color(0xFF000000).withOpacity(0.12),
      offset: const Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];

  // 按钮阴影
  static List<BoxShadow> get buttonShadow => [
    BoxShadow(
      color: const Color(0xFF000000).withOpacity(0.1),
      offset: const Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];

  // 浮动阴影
  static List<BoxShadow> get elevatedShadow => [
    BoxShadow(
      color: const Color(0xFF000000).withOpacity(0.1),
      offset: const Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -1,
    ),
    BoxShadow(
      color: const Color(0xFF000000).withOpacity(0.06),
      offset: const Offset(0, 2),
      blurRadius: 4,
      spreadRadius: -1,
    ),
  ];

  // === 动画系统 ===

  // 动画时长
  static const int animationDurationMs = 300;
  static const int fastAnimationDurationMs = 150;
  static const int slowAnimationDurationMs = 500;
  static const int pageTransitionDurationMs = 250;

  // 动画曲线
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve slowCurve = Curves.easeInOutCubic;
  static const Curve bounceCurve = Curves.elasticOut;

  // === 字体系统 ===

  // 字体大小
  static const double displayLargeFontSize = 57.0;
  static const double displayMediumFontSize = 45.0;
  static const double displaySmallFontSize = 36.0;
  static const double headlineLargeFontSize = 32.0;
  static const double headlineMediumFontSize = 28.0;
  static const double headlineSmallFontSize = 24.0;
  static const double titleLargeFontSize = 22.0;
  static const double titleMediumFontSize = 16.0;
  static const double titleSmallFontSize = 14.0;
  static const double labelLargeFontSize = 14.0;
  static const double labelMediumFontSize = 12.0;
  static const double labelSmallFontSize = 11.0;
  static const double bodyLargeFontSize = 16.0;
  static const double bodyMediumFontSize = 14.0;
  static const double bodySmallFontSize = 12.0;

  // 兼容旧版本的字体大小
  static const double titleFontSize = titleLargeFontSize;
  static const double subtitleFontSize = titleMediumFontSize;
  static const double bodyFontSize = bodyLargeFontSize;
  static const double captionFontSize = bodySmallFontSize;

  // === 间距系统 ===

  // 基础间距单位 (4px)
  static const double spaceUnit = 4.0;

  // 间距等级
  static const double spaceXs = spaceUnit; // 4px
  static const double spaceSm = spaceUnit * 2; // 8px
  static const double spaceMd = spaceUnit * 3; // 12px
  static const double spaceLg = spaceUnit * 4; // 16px
  static const double spaceXl = spaceUnit * 5; // 20px
  static const double space2Xl = spaceUnit * 6; // 24px
  static const double space3Xl = spaceUnit * 8; // 32px
  static const double space4Xl = spaceUnit * 10; // 40px
  static const double space5Xl = spaceUnit * 12; // 48px
  static const double space6Xl = spaceUnit * 16; // 64px

  // 兼容旧版本的间距
  static const double paddingSmall = spaceSm;
  static const double paddingMedium = spaceLg;
  static const double paddingLarge = space2Xl;
  static const double paddingXLarge = space3Xl;

  // === 圆角系统 ===

  // 圆角等级
  static const double radiusXs = 2.0;
  static const double radiusSm = 4.0;
  static const double radiusMd = 6.0;
  static const double radiusLg = 8.0;
  static const double radiusXl = 12.0;
  static const double radius2Xl = 16.0;
  static const double radius3Xl = 24.0;
  static const double radiusFull = 9999.0;

  // 兼容旧版本的圆角
  static const double borderRadiusSmall = radiusSm;
  static const double borderRadiusMedium = radiusLg;
  static const double borderRadiusLarge = radiusXl;

  // === 组件尺寸 ===

  // 按钮尺寸
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;
  static const double buttonHeightXLarge = 56.0;
  static const double buttonMinWidth = 64.0;

  // 兼容旧版本
  static const double buttonHeight = buttonHeightLarge;

  // 输入框尺寸
  static const double inputFieldHeightSmall = 40.0;
  static const double inputFieldHeightMedium = 48.0;
  static const double inputFieldHeightLarge = 56.0;

  // 兼容旧版本
  static const double inputFieldHeight = inputFieldHeightLarge;

  // 图标尺寸
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 20.0;
  static const double iconSizeLarge = 24.0;
  static const double iconSizeXLarge = 32.0;

  // 头像尺寸
  static const double avatarSizeSmall = 24.0;
  static const double avatarSizeMedium = 32.0;
  static const double avatarSizeLarge = 40.0;
  static const double avatarSizeXLarge = 48.0;

  // === 断点系统 (响应式设计) ===

  static const double breakpointMobile = 480.0;
  static const double breakpointTablet = 768.0;
  static const double breakpointDesktop = 1024.0;
  static const double breakpointWide = 1280.0;

  // === 工具方法 ===

  /// 获取主色渐变
  static LinearGradient getPrimaryGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(begin: begin, end: end, colors: primaryGradient);
  }

  /// 获取次要色渐变
  static LinearGradient getSecondaryGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(begin: begin, end: end, colors: secondaryGradient);
  }

  /// 获取强调色渐变
  static LinearGradient getAccentGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(begin: begin, end: end, colors: accentGradient);
  }

  /// 获取背景渐变
  static LinearGradient getBackgroundGradient({
    AlignmentGeometry begin = Alignment.topCenter,
    AlignmentGeometry end = Alignment.bottomCenter,
  }) {
    return LinearGradient(begin: begin, end: end, colors: backgroundGradient);
  }
}
