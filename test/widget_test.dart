// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:mental_math_trainer/main.dart';

void main() {
  testWidgets('App starts with home screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MentalMathTrainerApp());

    // Verify that the app title is displayed.
    expect(find.text('心算训练'), findsOneWidget);

    // Verify that the start training button is displayed.
    expect(find.text('开始训练'), findsOneWidget);

    // Verify that difficulty selection is displayed.
    expect(find.text('选择难度'), findsOneWidget);
    expect(find.text('简单'), findsOneWidget);
    expect(find.text('中等'), findsOneWidget);
    expect(find.text('困难'), findsOneWidget);
  });
}
